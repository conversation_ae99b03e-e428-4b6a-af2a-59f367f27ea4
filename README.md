# متصفح الويب - Python Browser

متصفح ويب بسيط مكتوب بلغة Python يمكنك من تصفح المواقع الإلكترونية.

## المميزات

- 🌐 تصفح المواقع الإلكترونية
- 📚 حفظ المواقع المفضلة
- 📜 تاريخ التصفح
- 🔄 أزرار التنقل (رجوع، تقدم، تحديث)
- 🏠 زر الصفحة الرئيسية
- 💾 حفظ الإعدادات تلقائياً
- 🎨 واجهة مستخدم عربية

## الملفات المتوفرة

1. **متصفح_ويب.py** - النسخة الأساسية باستخدام tkinter
2. **متصفح_ويب_محسن.py** - النسخة المحسنة باستخدام webview
3. **requirements.txt** - المكتبات المطلوبة

## التثبيت والتشغيل

### 1. تثبيت المكتبات المطلوبة

```bash
pip install -r requirements.txt
```

أو تثبيت المكتبات يدوياً:

```bash
pip install pywebview requests
```

### 2. تشغيل المتصفح

#### النسخة الأساسية:
```bash
python متصفح_ويب.py
```

#### النسخة المحسنة:
```bash
python متصفح_ويب_محسن.py
```

## كيفية الاستخدام

### النسخة الأساسية (tkinter)
- أدخل عنوان الموقع في شريط العنوان
- اضغط Enter أو زر "اذهب"
- استخدم الأزرار للتنقل والتحكم
- يمكنك إضافة المواقع للمفضلة
- عرض تاريخ التصفح من التبويب المخصص

### النسخة المحسنة (webview)
- واجهة أكثر حداثة وجمالاً
- عرض المواقع داخل البرنامج نفسه
- روابط سريعة للمواقع الشهيرة
- شريط حالة يعرض معلومات التحميل

## الوظائف المتاحة

### أزرار التنقل
- **رجوع (←)**: العودة للصفحة السابقة
- **تقدم (→)**: الانتقال للصفحة التالية
- **تحديث (⟳)**: إعادة تحميل الصفحة الحالية
- **الرئيسية (🏠)**: الذهاب لصفحة جوجل

### المفضلة
- **إضافة للمفضلة (⭐)**: حفظ الموقع الحالي
- **عرض المفضلة (📚)**: عرض قائمة المواقع المحفوظة

### التاريخ
- حفظ تلقائي لتاريخ التصفح
- إمكانية الوصول للمواقع المزارة سابقاً
- مسح التاريخ

## الملفات المُنشأة تلقائياً

- **browser_settings.json**: ملف حفظ الإعدادات والمفضلة والتاريخ

## متطلبات النظام

- Python 3.6 أو أحدث
- نظام التشغيل: Windows, macOS, Linux
- اتصال بالإنترنت للتصفح

## ملاحظات مهمة

1. **الأمان**: المتصفح أساسي ولا يحتوي على ميزات أمان متقدمة
2. **الأداء**: قد يكون أبطأ من المتصفحات التجارية
3. **التوافق**: بعض المواقع قد لا تعمل بشكل مثالي
4. **التطوير**: هذا مشروع تعليمي قابل للتطوير والتحسين

## التطوير والتحسين

يمكنك تطوير المتصفح بإضافة:
- دعم التبويبات المتعددة
- مدير التحميلات
- حظر الإعلانات
- ميزات أمان إضافية
- دعم الإضافات

## المساعدة والدعم

إذا واجهت أي مشاكل:
1. تأكد من تثبيت جميع المكتبات المطلوبة
2. تحقق من اتصال الإنترنت
3. جرب إعادة تشغيل البرنامج

---

**ملاحظة**: هذا المتصفح مخصص للأغراض التعليمية والتجريبية. للاستخدام اليومي، يُنصح باستخدام متصفحات تجارية مثل Chrome أو Firefox.
