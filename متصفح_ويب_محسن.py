#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
متصفح ويب محسن باستخدام Python و WebView
يعرض المواقع داخل البرنامج نفسه
"""

import webview
import threading
import json
import os
from urllib.parse import urlparse
import time

class متصفح_ويب_محسن:
    def __init__(self):
        # إعدادات المتصفح
        self.current_url = "https://www.google.com"
        self.history = []
        self.bookmarks = []
        self.window = None

        # تحميل الإعدادات
        self.load_settings()

    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        try:
            if os.path.exists("browser_settings.json"):
                with open("browser_settings.json", "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.bookmarks = data.get("bookmarks", [])
                    self.history = data.get("history", [])
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            data = {
                "bookmarks": self.bookmarks,
                "history": self.history
            }
            with open("browser_settings.json", "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")

    def add_to_history(self, url):
        """إضافة URL إلى التاريخ"""
        if url and url not in self.history:
            self.history.append(url)
            # الاحتفاظ بآخر 50 موقع فقط
            if len(self.history) > 50:
                self.history = self.history[-50:]

    def navigate_to(self, url):
        """التنقل إلى URL محدد"""
        try:
            # التأكد من وجود بروتوكول
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url

            self.current_url = url
            self.add_to_history(url)

            # إذا كانت النافذة موجودة، قم بتحديث URL
            if self.window:
                self.window.load_url(url)

            return True
        except Exception as e:
            print(f"خطأ في التنقل: {e}")
            return False

    def create_html_interface(self):
        """إنشاء واجهة HTML للمتصفح"""
        html_content = """
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>متصفح الويب</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 0;
                    background: #f5f5f5;
                    direction: rtl;
                }

                .toolbar {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .nav-buttons {
                    display: flex;
                    gap: 5px;
                }

                .nav-btn {
                    background: rgba(255,255,255,0.2);
                    border: none;
                    color: white;
                    padding: 8px 12px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background 0.3s;
                }

                .nav-btn:hover {
                    background: rgba(255,255,255,0.3);
                }

                .url-container {
                    flex: 1;
                    display: flex;
                    gap: 10px;
                    align-items: center;
                }

                .url-input {
                    flex: 1;
                    padding: 10px;
                    border: none;
                    border-radius: 25px;
                    font-size: 14px;
                    outline: none;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }

                .go-btn {
                    background: #4CAF50;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 25px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background 0.3s;
                }

                .go-btn:hover {
                    background: #45a049;
                }

                .content-area {
                    height: calc(100vh - 80px);
                    background: white;
                    margin: 10px;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    overflow: hidden;
                }

                .welcome-screen {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100%;
                    text-align: center;
                    color: #666;
                }

                .welcome-screen h1 {
                    color: #333;
                    margin-bottom: 20px;
                }

                .quick-links {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 15px;
                    margin-top: 30px;
                    max-width: 800px;
                }

                .quick-link {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px;
                    border-radius: 10px;
                    text-decoration: none;
                    transition: transform 0.3s;
                    text-align: center;
                }

                .quick-link:hover {
                    transform: translateY(-5px);
                }

                .browser-frame {
                    width: 100%;
                    height: 100%;
                    border: none;
                    display: none;
                }

                .status-bar {
                    background: #333;
                    color: white;
                    padding: 5px 15px;
                    font-size: 12px;
                    position: fixed;
                    bottom: 0;
                    left: 0;
                    right: 0;
                }
            </style>
        </head>
        <body>
            <div class="toolbar">
                <div class="nav-buttons">
                    <button class="nav-btn" onclick="goBack()">← رجوع</button>
                    <button class="nav-btn" onclick="goForward()">تقدم →</button>
                    <button class="nav-btn" onclick="refresh()">⟳ تحديث</button>
                    <button class="nav-btn" onclick="goHome()">🏠 الرئيسية</button>
                </div>

                <div class="url-container">
                    <input type="text" class="url-input" id="urlInput"
                           placeholder="أدخل عنوان الموقع هنا..."
                           onkeypress="handleEnter(event)">
                    <button class="go-btn" onclick="navigate()">اذهب</button>
                </div>

                <div class="nav-buttons">
                    <button class="nav-btn" onclick="showBookmarks()">📚 المفضلة</button>
                    <button class="nav-btn" onclick="addBookmark()">⭐ إضافة</button>
                </div>
            </div>

            <div class="content-area">
                <div class="welcome-screen" id="welcomeScreen">
                    <h1>مرحباً بك في متصفح الويب</h1>
                    <p>أدخل عنوان الموقع في الأعلى للبدء في التصفح</p>

                    <div class="quick-links">
                        <a href="#" class="quick-link" onclick="navigateToUrl('https://www.google.com')">
                            <h3>🔍 جوجل</h3>
                            <p>محرك البحث الأشهر</p>
                        </a>
                        <a href="#" class="quick-link" onclick="navigateToUrl('https://www.youtube.com')">
                            <h3>📺 يوتيوب</h3>
                            <p>مقاطع الفيديو</p>
                        </a>
                        <a href="#" class="quick-link" onclick="navigateToUrl('https://www.facebook.com')">
                            <h3>📘 فيسبوك</h3>
                            <p>التواصل الاجتماعي</p>
                        </a>
                        <a href="#" class="quick-link" onclick="navigateToUrl('https://www.twitter.com')">
                            <h3>🐦 تويتر</h3>
                            <p>الأخبار والتحديثات</p>
                        </a>
                    </div>
                </div>

                <iframe class="browser-frame" id="browserFrame"></iframe>
            </div>

            <div class="status-bar" id="statusBar">
                جاهز للتصفح
            </div>

            <script>
                // متغيرات عامة
                let currentUrl = '';
                let history = [];
                let historyIndex = -1;

                // وظائف التنقل
                function navigate() {
                    const url = document.getElementById('urlInput').value.trim();
                    if (url) {
                        navigateToUrl(url);
                    }
                }

                function navigateToUrl(url) {
                    // إضافة https إذا لم يكن موجوداً
                    if (!url.startsWith('http://') && !url.startsWith('https://')) {
                        url = 'https://' + url;
                    }

                    currentUrl = url;
                    document.getElementById('urlInput').value = url;

                    // إخفاء شاشة الترحيب وإظهار الإطار
                    document.getElementById('welcomeScreen').style.display = 'none';
                    const frame = document.getElementById('browserFrame');
                    frame.style.display = 'block';
                    frame.src = url;

                    // إضافة إلى التاريخ
                    addToHistory(url);

                    // تحديث شريط الحالة
                    updateStatus('جاري تحميل: ' + url);
                }

                function addToHistory(url) {
                    // إزالة العناصر التالية إذا كنا في وسط التاريخ
                    if (historyIndex < history.length - 1) {
                        history = history.slice(0, historyIndex + 1);
                    }

                    history.push(url);
                    historyIndex = history.length - 1;
                }

                function goBack() {
                    if (historyIndex > 0) {
                        historyIndex--;
                        navigateToUrl(history[historyIndex]);
                    }
                }

                function goForward() {
                    if (historyIndex < history.length - 1) {
                        historyIndex++;
                        navigateToUrl(history[historyIndex]);
                    }
                }

                function refresh() {
                    if (currentUrl) {
                        document.getElementById('browserFrame').src = currentUrl;
                        updateStatus('جاري تحديث: ' + currentUrl);
                    }
                }

                function goHome() {
                    navigateToUrl('https://www.google.com');
                }

                function handleEnter(event) {
                    if (event.key === 'Enter') {
                        navigate();
                    }
                }

                function updateStatus(message) {
                    document.getElementById('statusBar').textContent = message;
                }

                function showBookmarks() {
                    alert('وظيفة المفضلة قيد التطوير');
                }

                function addBookmark() {
                    if (currentUrl) {
                        const name = prompt('أدخل اسماً للمفضلة:');
                        if (name) {
                            alert('تم إضافة الموقع للمفضلة: ' + name);
                        }
                    } else {
                        alert('لا يوجد موقع لإضافته للمفضلة');
                    }
                }

                // تحديث شريط الحالة عند تحميل الإطار
                document.getElementById('browserFrame').onload = function() {
                    updateStatus('تم تحميل: ' + currentUrl);
                };

                // رسالة ترحيب
                updateStatus('مرحباً بك في متصفح الويب - أدخل عنوان الموقع للبدء');
            </script>
        </body>
        </html>
        """
        return html_content

    def run(self):
        """تشغيل المتصفح"""
        try:
            # إنشاء نافذة webview
            self.window = webview.create_window(
                title='متصفح الويب - Python Browser',
                html=self.create_html_interface(),
                width=1200,
                height=800,
                min_size=(800, 600),
                resizable=True
            )

            # تشغيل webview
            webview.start(debug=False)

        except Exception as e:
            print(f"خطأ في تشغيل المتصفح: {e}")
        finally:
            # حفظ الإعدادات عند الإغلاق
            self.save_settings()

if __name__ == "__main__":
    # تشغيل المتصفح المحسن
    browser = متصفح_ويب_محسن()
    browser.run()