@echo off
chcp 65001 > nul
echo ========================================
echo       متصفح الويب - Python Browser
echo ========================================
echo.
echo اختر النسخة التي تريد تشغيلها:
echo.
echo 1. النسخة البسيطة (لا تحتاج مكتبات إضافية)
echo 2. النسخة الأساسية (tkinter)
echo 3. النسخة المحسنة (webview)
echo 4. تثبيت المكتبات المطلوبة
echo 5. خروج
echo.
set /p choice="أدخل اختيارك (1-5): "

if "%choice%"=="1" (
    echo.
    echo جاري تشغيل النسخة البسيطة...
    python متصفح_بسيط.py
) else if "%choice%"=="2" (
    echo.
    echo جاري تشغيل النسخة الأساسية...
    python متصفح_ويب.py
) else if "%choice%"=="3" (
    echo.
    echo جاري تشغيل النسخة المحسنة...
    python متصفح_ويب_محسن.py
) else if "%choice%"=="4" (
    echo.
    echo جاري تثبيت المكتبات المطلوبة...
    pip install -r requirements.txt
    echo.
    echo تم تثبيت المكتبات بنجاح!
    pause
) else if "%choice%"=="5" (
    echo.
    echo شكراً لاستخدام متصفح الويب!
    exit
) else (
    echo.
    echo اختيار غير صحيح، يرجى المحاولة مرة أخرى.
    pause
    goto start
)

pause
