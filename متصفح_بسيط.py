#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
متصفح ويب بسيط جداً - يفتح المواقع في المتصفح الافتراضي
لا يحتاج مكتبات إضافية
"""

import tkinter as tk
from tkinter import messagebox, simpledialog
import webbrowser
import json
import os
from datetime import datetime

class متصفح_بسيط:
    def __init__(self):
        # إنشاء النافذة الرئيسية
        self.root = tk.Tk()
        self.root.title("متصفح الويب البسيط")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f8ff')
        
        # متغيرات البرنامج
        self.history = []
        self.bookmarks = []
        
        # تحميل البيانات المحفوظة
        self.load_data()
        
        # إنشاء الواجهة
        self.create_interface()
        
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        
        # العنوان الرئيسي
        title_label = tk.Label(self.root, text="متصفح الويب البسيط", 
                              font=('Arial', 20, 'bold'), 
                              bg='#f0f8ff', fg='#2c3e50')
        title_label.pack(pady=20)
        
        # إطار شريط العنوان
        url_frame = tk.Frame(self.root, bg='#f0f8ff')
        url_frame.pack(pady=10, padx=20, fill=tk.X)
        
        # تسمية شريط العنوان
        url_label = tk.Label(url_frame, text="عنوان الموقع:", 
                           font=('Arial', 12), bg='#f0f8ff')
        url_label.pack(side=tk.RIGHT, padx=5)
        
        # مربع إدخال العنوان
        self.url_entry = tk.Entry(url_frame, font=('Arial', 14), 
                                 relief=tk.RAISED, bd=2, width=50)
        self.url_entry.pack(side=tk.RIGHT, padx=10, fill=tk.X, expand=True)
        self.url_entry.bind('<Return>', self.open_website)
        
        # زر الذهاب
        go_button = tk.Button(url_frame, text="اذهب", 
                            command=self.open_website,
                            font=('Arial', 12, 'bold'),
                            bg='#3498db', fg='white',
                            relief=tk.RAISED, bd=3,
                            cursor='hand2')
        go_button.pack(side=tk.RIGHT, padx=5)
        
        # إطار الأزرار السريعة
        quick_frame = tk.Frame(self.root, bg='#f0f8ff')
        quick_frame.pack(pady=20)
        
        # أزرار المواقع السريعة
        quick_sites = [
            ("🔍 جوجل", "https://www.google.com"),
            ("📺 يوتيوب", "https://www.youtube.com"),
            ("📘 فيسبوك", "https://www.facebook.com"),
            ("🐦 تويتر", "https://www.twitter.com"),
            ("📰 الجزيرة", "https://www.aljazeera.net"),
            ("🛒 أمازون", "https://www.amazon.com")
        ]
        
        for i, (name, url) in enumerate(quick_sites):
            row = i // 3
            col = i % 3
            
            btn = tk.Button(quick_frame, text=name,
                          command=lambda u=url: self.open_url(u),
                          font=('Arial', 11),
                          bg='#e74c3c', fg='white',
                          relief=tk.RAISED, bd=2,
                          cursor='hand2',
                          width=15, height=2)
            btn.grid(row=row, column=col, padx=10, pady=5)
        
        # إطار الأدوات
        tools_frame = tk.Frame(self.root, bg='#f0f8ff')
        tools_frame.pack(pady=20)
        
        # أزرار الأدوات
        tools_buttons = [
            ("📚 المفضلة", self.show_bookmarks, '#9b59b6'),
            ("⭐ إضافة للمفضلة", self.add_bookmark, '#f39c12'),
            ("📜 التاريخ", self.show_history, '#2ecc71'),
            ("🗑️ مسح التاريخ", self.clear_history, '#e67e22')
        ]
        
        for i, (text, command, color) in enumerate(tools_buttons):
            btn = tk.Button(tools_frame, text=text,
                          command=command,
                          font=('Arial', 10),
                          bg=color, fg='white',
                          relief=tk.RAISED, bd=2,
                          cursor='hand2',
                          width=18)
            btn.grid(row=i//2, column=i%2, padx=10, pady=5)
        
        # منطقة المعلومات
        info_frame = tk.Frame(self.root, bg='#ecf0f1', relief=tk.SUNKEN, bd=2)
        info_frame.pack(pady=20, padx=20, fill=tk.BOTH, expand=True)
        
        info_label = tk.Label(info_frame, 
                            text="معلومات المتصفح",
                            font=('Arial', 14, 'bold'),
                            bg='#ecf0f1', fg='#2c3e50')
        info_label.pack(pady=10)
        
        # معلومات الاستخدام
        self.info_text = tk.Text(info_frame, height=8, width=70,
                               font=('Arial', 10),
                               bg='white', fg='#2c3e50',
                               relief=tk.SUNKEN, bd=1)
        self.info_text.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)
        
        # إدراج معلومات ترحيبية
        welcome_text = """مرحباً بك في متصفح الويب البسيط!

كيفية الاستخدام:
• أدخل عنوان الموقع في الحقل أعلاه واضغط Enter أو "اذهب"
• استخدم الأزرار السريعة للوصول للمواقع الشهيرة
• يمكنك إضافة المواقع للمفضلة وعرض تاريخ التصفح
• المواقع ستفتح في متصفحك الافتراضي

ملاحظة: هذا المتصفح يستخدم متصفح النظام الافتراضي لعرض المواقع.
"""
        self.info_text.insert(tk.END, welcome_text)
        self.info_text.config(state=tk.DISABLED)
        
        # شريط الحالة
        self.status_bar = tk.Label(self.root, text="جاهز للتصفح", 
                                 relief=tk.SUNKEN, anchor=tk.W,
                                 bg='#34495e', fg='white',
                                 font=('Arial', 9))
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def open_website(self, event=None):
        """فتح الموقع المدخل"""
        url = self.url_entry.get().strip()
        if url:
            self.open_url(url)
        else:
            messagebox.showwarning("تحذير", "يرجى إدخال عنوان الموقع")
            
    def open_url(self, url):
        """فتح URL محدد"""
        try:
            # إضافة http إذا لم يكن موجوداً
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
                
            # فتح الموقع في المتصفح الافتراضي
            webbrowser.open(url)
            
            # إضافة إلى التاريخ
            self.add_to_history(url)
            
            # تحديث شريط العنوان
            self.url_entry.delete(0, tk.END)
            self.url_entry.insert(0, url)
            
            # تحديث شريط الحالة
            self.status_bar.config(text=f"تم فتح: {url}")
            
            # تحديث منطقة المعلومات
            self.update_info_display()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء فتح الموقع:\n{str(e)}")
            self.status_bar.config(text="خطأ في فتح الموقع")
            
    def add_to_history(self, url):
        """إضافة URL إلى التاريخ"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        history_entry = {"url": url, "time": timestamp}
        
        # إزالة الدخول المكرر إن وجد
        self.history = [h for h in self.history if h["url"] != url]
        
        # إضافة في المقدمة
        self.history.insert(0, history_entry)
        
        # الاحتفاظ بآخر 100 موقع فقط
        if len(self.history) > 100:
            self.history = self.history[:100]
            
        # حفظ البيانات
        self.save_data()
        
    def add_bookmark(self):
        """إضافة موقع للمفضلة"""
        url = self.url_entry.get().strip()
        if not url:
            messagebox.showwarning("تحذير", "لا يوجد موقع لإضافته للمفضلة")
            return
            
        name = simpledialog.askstring("اسم المفضلة", 
                                    "أدخل اسماً للموقع:",
                                    initialvalue=url)
        if name:
            bookmark = {"name": name, "url": url}
            
            # التحقق من عدم وجود الموقع مسبقاً
            if not any(b["url"] == url for b in self.bookmarks):
                self.bookmarks.append(bookmark)
                self.save_data()
                messagebox.showinfo("نجح", f"تم إضافة '{name}' للمفضلة")
            else:
                messagebox.showinfo("معلومات", "الموقع موجود في المفضلة مسبقاً")
                
    def show_bookmarks(self):
        """عرض المفضلة"""
        if not self.bookmarks:
            messagebox.showinfo("المفضلة", "لا توجد مواقع في المفضلة")
            return
            
        # نافذة المفضلة
        bookmarks_window = tk.Toplevel(self.root)
        bookmarks_window.title("المواقع المفضلة")
        bookmarks_window.geometry("500x400")
        bookmarks_window.configure(bg='#f0f8ff')
        
        tk.Label(bookmarks_window, text="المواقع المفضلة", 
               font=('Arial', 16, 'bold'),
               bg='#f0f8ff', fg='#2c3e50').pack(pady=10)
        
        # قائمة المفضلة
        listbox_frame = tk.Frame(bookmarks_window)
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        scrollbar = tk.Scrollbar(listbox_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        bookmarks_listbox = tk.Listbox(listbox_frame, font=('Arial', 10),
                                     yscrollcommand=scrollbar.set)
        bookmarks_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=bookmarks_listbox.yview)
        
        for bookmark in self.bookmarks:
            bookmarks_listbox.insert(tk.END, f"{bookmark['name']} - {bookmark['url']}")
            
        # أزرار المفضلة
        buttons_frame = tk.Frame(bookmarks_window, bg='#f0f8ff')
        buttons_frame.pack(pady=10)
        
        def open_selected():
            selection = bookmarks_listbox.curselection()
            if selection:
                bookmark = self.bookmarks[selection[0]]
                self.open_url(bookmark['url'])
                bookmarks_window.destroy()
                
        def delete_selected():
            selection = bookmarks_listbox.curselection()
            if selection:
                if messagebox.askyesno("تأكيد", "هل تريد حذف هذه المفضلة؟"):
                    del self.bookmarks[selection[0]]
                    bookmarks_listbox.delete(selection[0])
                    self.save_data()
                    
        tk.Button(buttons_frame, text="فتح الموقع", command=open_selected,
                 bg='#3498db', fg='white', font=('Arial', 10)).pack(side=tk.LEFT, padx=5)
        tk.Button(buttons_frame, text="حذف", command=delete_selected,
                 bg='#e74c3c', fg='white', font=('Arial', 10)).pack(side=tk.LEFT, padx=5)
        
    def show_history(self):
        """عرض التاريخ"""
        if not self.history:
            messagebox.showinfo("التاريخ", "لا يوجد تاريخ تصفح")
            return
            
        # نافذة التاريخ
        history_window = tk.Toplevel(self.root)
        history_window.title("تاريخ التصفح")
        history_window.geometry("600x400")
        history_window.configure(bg='#f0f8ff')
        
        tk.Label(history_window, text="تاريخ التصفح", 
               font=('Arial', 16, 'bold'),
               bg='#f0f8ff', fg='#2c3e50').pack(pady=10)
        
        # قائمة التاريخ
        listbox_frame = tk.Frame(history_window)
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        scrollbar = tk.Scrollbar(listbox_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        history_listbox = tk.Listbox(listbox_frame, font=('Arial', 9),
                                   yscrollcommand=scrollbar.set)
        history_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=history_listbox.yview)
        
        for entry in self.history:
            history_listbox.insert(tk.END, f"{entry['time']} - {entry['url']}")
            
        # زر فتح من التاريخ
        def open_from_history():
            selection = history_listbox.curselection()
            if selection:
                entry = self.history[selection[0]]
                self.open_url(entry['url'])
                history_window.destroy()
                
        tk.Button(history_window, text="فتح الموقع", command=open_from_history,
                 bg='#2ecc71', fg='white', font=('Arial', 10)).pack(pady=10)
        
    def clear_history(self):
        """مسح التاريخ"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح تاريخ التصفح؟"):
            self.history.clear()
            self.save_data()
            self.update_info_display()
            messagebox.showinfo("تم", "تم مسح تاريخ التصفح")
            
    def update_info_display(self):
        """تحديث منطقة المعلومات"""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        
        info = f"""إحصائيات التصفح:
• عدد المواقع في التاريخ: {len(self.history)}
• عدد المواقع المفضلة: {len(self.bookmarks)}

آخر المواقع المزارة:
"""
        
        for i, entry in enumerate(self.history[:5]):
            info += f"• {entry['url']} ({entry['time']})\n"
            
        if len(self.history) > 5:
            info += f"... و {len(self.history) - 5} موقع آخر\n"
            
        self.info_text.insert(tk.END, info)
        self.info_text.config(state=tk.DISABLED)
        
    def load_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            if os.path.exists("browser_data.json"):
                with open("browser_data.json", "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.history = data.get("history", [])
                    self.bookmarks = data.get("bookmarks", [])
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            
    def save_data(self):
        """حفظ البيانات"""
        try:
            data = {
                "history": self.history,
                "bookmarks": self.bookmarks
            }
            with open("browser_data.json", "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ البيانات: {e}")
            
    def run(self):
        """تشغيل المتصفح"""
        # حفظ البيانات عند الإغلاق
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """عند إغلاق البرنامج"""
        self.save_data()
        self.root.destroy()

if __name__ == "__main__":
    # تشغيل المتصفح البسيط
    browser = متصفح_بسيط()
    browser.run()
