# المكتبات المطلوبة لتشغيل متصفح الويب

# للنسخة المحسنة (متصفح_ويب_محسن.py)
pywebview>=4.0.0

# للميزات الإضافية (اختيارية)
requests>=2.25.0

# المكتبات المدمجة مع Python (لا تحتاج تثبيت)
# tkinter - للواجهة الرسومية
# webbrowser - لفتح المواقع في المتصفح الافتراضي
# json - لحفظ الإعدادات
# os - للتعامل مع الملفات
# datetime - للتاريخ والوقت

# ملاحظة: النسخة البسيطة (متصفح_بسيط.py) لا تحتاج أي مكتبات إضافية
