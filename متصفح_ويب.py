#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
متصفح ويب بسيط باستخدام Python
يمكن استخدامه لتصفح المواقع الإلكترونية
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import webview
import threading
import webbrowser
import urllib.parse
import requests
from tkinter import scrolledtext
import os
import json

class متصفح_ويب:
    def __init__(self):
        # إنشاء النافذة الرئيسية
        self.root = tk.Tk()
        self.root.title("متصفح الويب - Python Browser")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # متغيرات المتصفح
        self.current_url = ""
        self.history = []  # تاريخ التصفح
        self.bookmarks = []  # المفضلة
        self.webview_window = None
        
        # تحميل الإعدادات والمفضلة
        self.load_settings()
        
        # إنشاء واجهة المستخدم
        self.create_interface()
        
    def create_interface(self):
        """إنشاء واجهة المستخدم الرسومية"""
        
        # شريط الأدوات العلوي
        toolbar_frame = tk.Frame(self.root, bg='#e0e0e0', height=50)
        toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        toolbar_frame.pack_propagate(False)
        
        # أزرار التنقل
        self.btn_back = tk.Button(toolbar_frame, text="← رجوع", command=self.go_back, 
                                 bg='#4CAF50', fg='white', font=('Arial', 10))
        self.btn_back.pack(side=tk.LEFT, padx=2)
        
        self.btn_forward = tk.Button(toolbar_frame, text="تقدم →", command=self.go_forward,
                                   bg='#4CAF50', fg='white', font=('Arial', 10))
        self.btn_forward.pack(side=tk.LEFT, padx=2)
        
        self.btn_refresh = tk.Button(toolbar_frame, text="⟳ تحديث", command=self.refresh_page,
                                   bg='#2196F3', fg='white', font=('Arial', 10))
        self.btn_refresh.pack(side=tk.LEFT, padx=2)
        
        self.btn_home = tk.Button(toolbar_frame, text="🏠 الرئيسية", command=self.go_home,
                                bg='#FF9800', fg='white', font=('Arial', 10))
        self.btn_home.pack(side=tk.LEFT, padx=2)
        
        # شريط العنوان (URL)
        url_frame = tk.Frame(toolbar_frame)
        url_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10)
        
        tk.Label(url_frame, text="العنوان:", bg='#e0e0e0', font=('Arial', 10)).pack(side=tk.LEFT)
        
        self.url_entry = tk.Entry(url_frame, font=('Arial', 12), relief=tk.SUNKEN, bd=2)
        self.url_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        self.url_entry.bind('<Return>', self.navigate_to_url)
        
        # زر الذهاب
        self.btn_go = tk.Button(url_frame, text="اذهب", command=self.navigate_to_url,
                              bg='#9C27B0', fg='white', font=('Arial', 10))
        self.btn_go.pack(side=tk.RIGHT, padx=2)
        
        # أزرار إضافية
        self.btn_bookmark = tk.Button(toolbar_frame, text="⭐ إضافة للمفضلة", 
                                    command=self.add_bookmark, bg='#FFC107', font=('Arial', 9))
        self.btn_bookmark.pack(side=tk.RIGHT, padx=2)
        
        self.btn_bookmarks = tk.Button(toolbar_frame, text="📚 المفضلة", 
                                     command=self.show_bookmarks, bg='#795548', fg='white', font=('Arial', 9))
        self.btn_bookmarks.pack(side=tk.RIGHT, padx=2)
        
        # منطقة المحتوى الرئيسية
        content_frame = tk.Frame(self.root)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # إنشاء notebook للتبويبات
        self.notebook = ttk.Notebook(content_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # التبويب الأول - المتصفح
        self.browser_frame = tk.Frame(self.notebook)
        self.notebook.add(self.browser_frame, text="المتصفح")
        
        # منطقة عرض الويب
        self.web_display_frame = tk.Frame(self.browser_frame, bg='white', relief=tk.SUNKEN, bd=2)
        self.web_display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # رسالة ترحيب
        welcome_label = tk.Label(self.web_display_frame, 
                               text="مرحباً بك في متصفح الويب\nأدخل عنوان الموقع في الأعلى واضغط Enter أو اذهب",
                               font=('Arial', 16), bg='white', fg='#666')
        welcome_label.pack(expand=True)
        
        # التبويب الثاني - التاريخ
        self.history_frame = tk.Frame(self.notebook)
        self.notebook.add(self.history_frame, text="التاريخ")
        self.create_history_tab()
        
        # شريط الحالة
        self.status_bar = tk.Label(self.root, text="جاهز", relief=tk.SUNKEN, 
                                 anchor=tk.W, bg='#e0e0e0', font=('Arial', 9))
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def create_history_tab(self):
        """إنشاء تبويب التاريخ"""
        tk.Label(self.history_frame, text="تاريخ التصفح", font=('Arial', 14, 'bold')).pack(pady=10)
        
        # قائمة التاريخ
        self.history_listbox = tk.Listbox(self.history_frame, font=('Arial', 10))
        self.history_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        self.history_listbox.bind('<Double-Button-1>', self.navigate_from_history)
        
        # أزرار التاريخ
        history_buttons_frame = tk.Frame(self.history_frame)
        history_buttons_frame.pack(pady=5)
        
        tk.Button(history_buttons_frame, text="مسح التاريخ", command=self.clear_history,
                 bg='#f44336', fg='white').pack(side=tk.LEFT, padx=5)
        
    def navigate_to_url(self, event=None):
        """التنقل إلى عنوان URL محدد"""
        url = self.url_entry.get().strip()
        
        if not url:
            messagebox.showwarning("تحذير", "يرجى إدخال عنوان الموقع")
            return
            
        # إضافة http:// إذا لم يكن موجوداً
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url
            
        try:
            self.current_url = url
            self.url_entry.delete(0, tk.END)
            self.url_entry.insert(0, url)
            
            # إضافة إلى التاريخ
            self.add_to_history(url)
            
            # تحديث شريط الحالة
            self.status_bar.config(text=f"جاري تحميل: {url}")
            
            # فتح الموقع في متصفح النظام (حل مؤقت)
            webbrowser.open(url)
            
            self.status_bar.config(text=f"تم فتح: {url}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل الموقع:\n{str(e)}")
            self.status_bar.config(text="خطأ في التحميل")
            
    def add_to_history(self, url):
        """إضافة URL إلى التاريخ"""
        if url not in self.history:
            self.history.append(url)
            self.history_listbox.insert(tk.END, url)
            
    def navigate_from_history(self, event):
        """التنقل من التاريخ"""
        selection = self.history_listbox.curselection()
        if selection:
            url = self.history_listbox.get(selection[0])
            self.url_entry.delete(0, tk.END)
            self.url_entry.insert(0, url)
            self.navigate_to_url()
            
    def clear_history(self):
        """مسح التاريخ"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح تاريخ التصفح؟"):
            self.history.clear()
            self.history_listbox.delete(0, tk.END)
            
    def add_bookmark(self):
        """إضافة موقع للمفضلة"""
        url = self.url_entry.get().strip()
        if not url:
            messagebox.showwarning("تحذير", "لا يوجد موقع لإضافته للمفضلة")
            return
            
        name = simpledialog.askstring("اسم المفضلة", "أدخل اسماً للمفضلة:")
        if name:
            bookmark = {"name": name, "url": url}
            self.bookmarks.append(bookmark)
            self.save_settings()
            messagebox.showinfo("نجح", "تم إضافة الموقع للمفضلة")
            
    def show_bookmarks(self):
        """عرض المفضلة"""
        if not self.bookmarks:
            messagebox.showinfo("المفضلة", "لا توجد مواقع في المفضلة")
            return
            
        # نافذة المفضلة
        bookmarks_window = tk.Toplevel(self.root)
        bookmarks_window.title("المفضلة")
        bookmarks_window.geometry("400x300")
        
        tk.Label(bookmarks_window, text="المواقع المفضلة", font=('Arial', 14, 'bold')).pack(pady=10)
        
        bookmarks_listbox = tk.Listbox(bookmarks_window, font=('Arial', 10))
        bookmarks_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        for bookmark in self.bookmarks:
            bookmarks_listbox.insert(tk.END, f"{bookmark['name']} - {bookmark['url']}")
            
        def navigate_to_bookmark():
            selection = bookmarks_listbox.curselection()
            if selection:
                bookmark = self.bookmarks[selection[0]]
                self.url_entry.delete(0, tk.END)
                self.url_entry.insert(0, bookmark['url'])
                self.navigate_to_url()
                bookmarks_window.destroy()
                
        tk.Button(bookmarks_window, text="اذهب إلى الموقع", command=navigate_to_bookmark,
                 bg='#4CAF50', fg='white').pack(pady=5)
                 
    def go_back(self):
        """الرجوع للصفحة السابقة"""
        messagebox.showinfo("معلومات", "وظيفة الرجوع قيد التطوير")
        
    def go_forward(self):
        """التقدم للصفحة التالية"""
        messagebox.showinfo("معلومات", "وظيفة التقدم قيد التطوير")
        
    def refresh_page(self):
        """تحديث الصفحة الحالية"""
        if self.current_url:
            self.navigate_to_url()
        else:
            messagebox.showinfo("معلومات", "لا يوجد صفحة لتحديثها")
            
    def go_home(self):
        """الذهاب للصفحة الرئيسية"""
        home_url = "https://www.google.com"
        self.url_entry.delete(0, tk.END)
        self.url_entry.insert(0, home_url)
        self.navigate_to_url()
        
    def load_settings(self):
        """تحميل الإعدادات والمفضلة"""
        try:
            if os.path.exists("browser_settings.json"):
                with open("browser_settings.json", "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.bookmarks = data.get("bookmarks", [])
                    self.history = data.get("history", [])
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            
    def save_settings(self):
        """حفظ الإعدادات والمفضلة"""
        try:
            data = {
                "bookmarks": self.bookmarks,
                "history": self.history
            }
            with open("browser_settings.json", "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            
    def run(self):
        """تشغيل المتصفح"""
        # حفظ الإعدادات عند الإغلاق
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()
        
    def on_closing(self):
        """عند إغلاق البرنامج"""
        self.save_settings()
        self.root.destroy()

if __name__ == "__main__":
    # تشغيل المتصفح
    browser = متصفح_ويب()
    browser.run()
