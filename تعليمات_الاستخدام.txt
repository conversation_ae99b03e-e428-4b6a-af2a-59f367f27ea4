===============================================
        تعليمات استخدام متصفح الويب Python
===============================================

مرحباً بك في متصفح الويب المكتوب بلغة Python!

===============================================
                النسخ المتوفرة
===============================================

1. النسخة البسيطة (متصفح_بسيط.py):
   - لا تحتاج مكتبات إضافية
   - تفتح المواقع في متصفح النظام الافتراضي
   - واجهة بسيطة وسهلة الاستخدام
   - مناسبة للمبتدئين

2. النسخة الأساسية (متصفح_ويب.py):
   - تستخدم مكتبة tkinter
   - واجهة أكثر تفصيلاً
   - تبويبات متعددة
   - إدارة التاريخ والمفضلة

3. النسخة المحسنة (متصفح_ويب_محسن.py):
   - تستخدم مكتبة webview
   - عرض المواقع داخل البرنامج
   - واجهة HTML حديثة
   - تجربة أقرب للمتصفحات الحقيقية

===============================================
                طريقة التشغيل
===============================================

الطريقة الأولى - استخدام ملف التشغيل:
1. انقر مرتين على ملف "تشغيل_المتصفح.bat"
2. اختر النسخة التي تريد تشغيلها
3. اتبع التعليمات على الشاشة

الطريقة الثانية - من سطر الأوامر:
1. افتح Command Prompt أو PowerShell
2. انتقل إلى مجلد البرنامج
3. اكتب أحد الأوامر التالية:
   - python متصفح_بسيط.py
   - python متصفح_ويب.py
   - python متصفح_ويب_محسن.py

===============================================
            تثبيت المكتبات المطلوبة
===============================================

للنسخة البسيطة:
- لا تحتاج أي مكتبات إضافية

للنسخة الأساسية:
- tkinter (مدمجة مع Python عادة)

للنسخة المحسنة:
- pip install pywebview

أو لتثبيت جميع المكتبات:
- pip install -r requirements.txt

===============================================
                كيفية الاستخدام
===============================================

1. إدخال عنوان الموقع:
   - اكتب عنوان الموقع في الحقل المخصص
   - اضغط Enter أو زر "اذهب"
   - مثال: google.com أو https://www.youtube.com

2. الأزرار السريعة:
   - استخدم الأزرار للوصول السريع للمواقع الشهيرة
   - جوجل، يوتيوب، فيسبوك، تويتر، إلخ

3. إدارة المفضلة:
   - اضغط "إضافة للمفضلة" لحفظ الموقع الحالي
   - اضغط "المفضلة" لعرض المواقع المحفوظة
   - يمكنك حذف المفضلة من نافذة المفضلة

4. تاريخ التصفح:
   - يتم حفظ تاريخ التصفح تلقائياً
   - اضغط "التاريخ" لعرض المواقع المزارة
   - يمكنك مسح التاريخ بالكامل

5. أزرار التنقل:
   - رجوع: العودة للصفحة السابقة
   - تقدم: الانتقال للصفحة التالية
   - تحديث: إعادة تحميل الصفحة
   - الرئيسية: الذهاب لصفحة جوجل

===============================================
                الملفات المُنشأة
===============================================

- browser_settings.json: إعدادات النسخة الأساسية والمحسنة
- browser_data.json: بيانات النسخة البسيطة

هذه الملفات تحتوي على:
- تاريخ التصفح
- المواقع المفضلة
- إعدادات البرنامج

===============================================
                نصائح مهمة
===============================================

1. الأمان:
   - هذا متصفح تعليمي، لا يحتوي على ميزات أمان متقدمة
   - تجنب إدخال معلومات حساسة
   - استخدم متصفحات تجارية للأعمال المهمة

2. الأداء:
   - قد يكون أبطأ من المتصفحات التجارية
   - بعض المواقع قد لا تعمل بشكل مثالي
   - النسخة البسيطة الأسرع والأكثر استقراراً

3. التوافق:
   - يعمل على Windows, macOS, Linux
   - يحتاج Python 3.6 أو أحدث
   - يحتاج اتصال بالإنترنت

===============================================
                حل المشاكل
===============================================

مشكلة: البرنامج لا يعمل
الحل: تأكد من تثبيت Python وجميع المكتبات المطلوبة

مشكلة: خطأ في تحميل الموقع
الحل: تحقق من اتصال الإنترنت وصحة عنوان الموقع

مشكلة: النسخة المحسنة لا تعمل
الحل: تأكد من تثبيت مكتبة pywebview

مشكلة: الواجهة تظهر بشكل غريب
الحل: تأكد من دعم النظام للغة العربية

===============================================
                التطوير والتحسين
===============================================

يمكنك تطوير المتصفح بإضافة:
- دعم التبويبات المتعددة
- مدير التحميلات
- حظر الإعلانات
- ميزات أمان إضافية
- دعم الإضافات
- محرك بحث مدمج

===============================================
                معلومات إضافية
===============================================

- المطور: مساعد الذكي الاصطناعي
- اللغة: Python 3
- الترخيص: مفتوح المصدر
- الغرض: تعليمي وتجريبي

للمساعدة أو الاستفسارات، يرجى مراجعة ملف README.md

شكراً لاستخدام متصفح الويب Python!
